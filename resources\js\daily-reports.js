// Global variables to store selected jobs and technicians
import Swal from "sweetalert2";

let selectedJobs = [];
let selectedTechnicians = [];
let selectedUnit = null;

// Modal variables
let modalSelectedJobs = [];
let modalSelectedTechnicians = [];
let modalSelectedUnit = null;

// Edit modal variables
let editModalSelectedJobs = [];
let editModalSelectedTechnicians = [];
let editModalSelectedUnit = null;

$(document).ready(function () {
    // Set default date filters to current month
    setDefaultDateFilters();

    // Load initial data
    loadDailyReportsData();

    // Set default date to today
    $("#date_in").val(new Date().toISOString().split("T")[0]);
    $("#modal_date_in").val(new Date().toISOString().split("T")[0]);

    // Event handlers
    $("#add-daily-report-btn").click(function () {
        resetModalForm();
        $("#add-daily-report-modal-label").text("Add New Daily Report");
    });

    // Modal form submit
    $("#modal-save-btn").click(function () {
        saveModalDailyReport();
    });

    // Edit modal form submit
    $("#edit-modal-save-btn").click(function () {
        saveEditModalDailyReport();
    });

    // Filter event handlers
    $("#search-input, #start-date, #end-date, #shift-filter").on(
        "input change",
        function () {
            loadDailyReportsData();
        }
    );

    // Unit search handlers
    $("#unit_search").on("input", function () {
        const search = $(this).val();
        if (search.length >= 2) {
            searchUnits(search);
        } else {
            $("#unit_dropdown").hide();
        }
    });

    // Hide dropdown when clicking outside
    $(document).click(function (e) {
        if (!$(e.target).closest("#unit_search, #unit_dropdown").length) {
            $("#unit_dropdown").hide();
        }
    });

    // Job management handlers
    $("#add_job_btn").click(function () {
        addJob();
    });

    $("#job_description_input").keypress(function (e) {
        if (e.which === 13) {
            // Enter key
            addJob();
        }
    });

    // Technician management handlers
    $("#add_technician_btn").click(function () {
        addTechnician();
    });

    $("#technician_name_input").keypress(function (e) {
        if (e.which === 13) {
            // Enter key
            addTechnician();
        }
    });

    // Image preview handlers
    $("#before_images").change(function () {
        previewImages(this, "#before-images-preview");
    });

    $("#after_images").change(function () {
        previewImages(this, "#after-images-preview");
    });

    // Modal image preview handlers
    $("#modal_before_images").change(function () {
        previewImages(this, "#modal-before-images-preview");
    });

    $("#modal_after_images").change(function () {
        previewImages(this, "#modal-after-images-preview");
    });

    $("#modal_unit_images").change(function () {
        previewImages(this, "#modal-unit-images-preview");
    });

    // Modal unit search handlers
    $("#modal_unit_search").on("input", function () {
        const search = $(this).val();
        if (search.length >= 2) {
            searchModalUnits(search);
        } else {
            $("#modal_unit_dropdown").hide();
        }
    });

    // Modal job management handlers
    $("#modal_add_job_btn").click(function () {
        addModalJob();
    });

    $("#modal_job_description_input").keypress(function (e) {
        if (e.which === 13) {
            // Enter key
            addModalJob();
        }
    });

    // Modal technician management handlers
    $("#modal_add_technician_btn").click(function () {
        addModalTechnician();
    });

    $("#modal_technician_name_input").keypress(function (e) {
        if (e.which === 13) {
            // Enter key
            addModalTechnician();
        }
    });

    // Edit modal unit search handlers
    $("#edit_modal_unit_search").on("input", function () {
        const search = $(this).val();
        if (search.length >= 2) {
            searchEditModalUnits(search);
        } else {
            $("#edit_modal_unit_dropdown").hide();
        }
    });

    // Edit modal job management handlers
    $("#edit_modal_add_job_btn").click(function () {
        addEditModalJob();
    });

    $("#edit_modal_job_description_input").keypress(function (e) {
        if (e.which === 13) {
            // Enter key
            addEditModalJob();
        }
    });

    // Edit modal technician management handlers
    $("#edit_modal_add_technician_btn").click(function () {
        addEditModalTechnician();
    });

    $("#edit_modal_technician_name_input").keypress(function (e) {
        if (e.which === 13) {
            // Enter key
            addEditModalTechnician();
        }
    });

    // Edit modal image preview handlers
    $("#edit_modal_before_images").change(function () {
        previewImages(this, "#edit-modal-before-images-preview");
    });

    $("#edit_modal_after_images").change(function () {
        previewImages(this, "#edit-modal-after-images-preview");
    });

    $("#edit_modal_unit_images").change(function () {
        previewImages(this, "#edit-modal-unit-images-preview");
    });

    // Hide dropdown when clicking outside
    $(document).click(function (e) {
        if (
            !$(e.target).closest(
                "#unit_search, #unit_dropdown, #modal_unit_search, #modal_unit_dropdown, #edit_modal_unit_search, #edit_modal_unit_dropdown"
            ).length
        ) {
            $(
                "#unit_dropdown, #modal_unit_dropdown, #edit_modal_unit_dropdown"
            ).hide();
        }
    });

    // Pagination handler
    $(document).on("click", ".pagination a", function (e) {
        e.preventDefault();
        const page = $(this).attr("href").split("page=")[1];
        loadDailyReportsData(page);
    });

    // Export handlers
    $("#export-pdf-btn").click(function () {
        exportToPdf();
    });

    $("#export-excel-btn").click(function () {
        exportToExcel();
    });
});

// Function to set default date filters
function setDefaultDateFilters() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Format dates as YYYY-MM-DD
    const startDate = firstDayOfMonth.toISOString().split("T")[0];
    const endDate = today.toISOString().split("T")[0];

    $("#start-date").val(startDate);
    $("#end-date").val(endDate);
}

// Function to show form
function showForm() {
    $("#form-card").show();
}

// Function to hide form
function hideForm() {
    $("#form-card").hide();
    resetForm();
}

// Function to reset form
function resetForm() {
    $("#daily-report-form")[0].reset();
    $("#daily-report-id").val("");
    $("#before-images-preview").empty();
    $("#after-images-preview").empty();

    // Reset unit selection
    selectedUnit = null;
    $("#unit_search").val("");
    $("#unit_id").val("");
    $("#unit_dropdown").hide();

    // Reset jobs and technicians
    selectedJobs = [];
    selectedTechnicians = [];
    updateJobsList();
    updateTechniciansList();

    // Set default date
    $("#date_in").val(new Date().toISOString().split("T")[0]);
}

// Function to load daily reports data
function loadDailyReportsData(page = 1) {
    const search = $("#search-input").val();
    const startDate = $("#start-date").val();
    const endDate = $("#end-date").val();
    const shift = $("#shift-filter").val();

    // Show loading skeleton
    $("#loading-skeleton").removeClass("d-none");
    $("#daily-reports-table tbody").empty();

    $.ajax({
        url: "/daily-reports/data",
        type: "GET",
        data: {
            page: page,
            search: search,
            start_date: startDate,
            end_date: endDate,
            shift: shift,
            per_page: 10,
        },
        success: function (response) {
            $("#loading-skeleton").addClass("d-none");
            renderTable(response);
            renderPagination(response);
        },
        error: function (xhr) {
            $("#loading-skeleton").addClass("d-none");
            Swal.fire({
                icon: "error",
                title: "Error",
                text: "Gagal memuat data. Silakan coba lagi.",
            });
        },
    });
}

// Function to render table data
function renderTable(response) {
    const data = response.data;
    let html = "";

    if (data.length === 0) {
        html = `
            <tr>
                <td colspan="6" class="text-center">
                    <div class="py-4">
                        <i class="mdi mdi-file-document-outline" style="font-size: 48px; color: #ccc;"></i>
                        <p class="text-muted mt-2">Tidak ada data daily report</p>
                    </div>
                </td>
            </tr>
        `;
    } else {
        data.forEach(function (item) {
            const dateIn = new Date(item.date_in).toLocaleDateString("id-ID");
            const technicians = item.technicians.map((t) => t.name).join(", ");

            // Create jobs display with highlighted text only
            let jobsDisplay = "";
            item.jobs.forEach(function (job, index) {
                if (index > 0) jobsDisplay += ", ";
                if (job.highlight) {
                    jobsDisplay += `<span class="highlighted-job-text">${job.job_description}</span>`;
                } else {
                    jobsDisplay += job.job_description;
                }
            });

            html += `
                <tr>
                    <td>
                        <strong>${
                            item.unit ? item.unit.unit_code : "-"
                        }</strong>
                        <br>
                        <small class="text-muted">${
                            item.unit ? item.unit.unit_type : ""
                        }</small>
                    </td>
                    <td>
                        ${dateIn}
                        <br>
                        <small class="text-muted">${item.hour_in} - ${
                item.hour_out
            }</small>
                    </td>
                    <td>
                        <span class="badge ${
                            item.shift === "DAY" ? "bg-warning" : "bg-info"
                        }">${item.shift}</span>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="${
                            item.problem || "-"
                        }">
                            ${item.problem || "-"}
                        </div>
                        ${
                            item.problem_component
                                ? `<small class="text-muted">${item.problem_component}</small>`
                                : ""
                        }
                        <br>
                        <small class="text-muted">Jobs: ${jobsDisplay}</small>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 150px;" title="${technicians}">
                            ${technicians}
                        </div>
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="window.viewDailyReport(${
                                item.daily_report_id
                            })" title="Lihat Detail">
                                <i class="mdi mdi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="window.editDailyReport(${
                                item.daily_report_id
                            })" title="Edit">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="window.deleteDailyReport(${
                                item.daily_report_id
                            })" title="Hapus">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    }

    $("#daily-reports-table tbody").html(html);
}

// Function to search units
function searchUnits(search) {
    $.ajax({
        url: "/daily-reports/units/search",
        type: "GET",
        data: { search: search },
        success: function (units) {
            let html = "";
            if (units.length === 0) {
                html =
                    '<div class="dropdown-item text-muted">Tidak ada unit ditemukan</div>';
            } else {
                units.forEach(function (unit) {
                    html += `
                        <div class="dropdown-item" data-unit-id="${unit.id}" data-unit-code="${unit.unit_code}" data-unit-type="${unit.unit_type}">
                            <strong>${unit.unit_code}</strong> - ${unit.unit_type}
                        </div>
                    `;
                });
            }
            $("#unit_dropdown").html(html).show();

            // Handle unit selection
            $("#unit_dropdown .dropdown-item").click(function () {
                if ($(this).data("unit-id")) {
                    selectedUnit = {
                        id: $(this).data("unit-id"),
                        unit_code: $(this).data("unit-code"),
                        unit_type: $(this).data("unit-type"),
                    };
                    $("#unit_search").val(
                        `${selectedUnit.unit_code} - ${selectedUnit.unit_type}`
                    );
                    $("#unit_id").val(selectedUnit.id);
                    $("#unit_dropdown").hide();
                }
            });
        },
        error: function () {
            $("#unit_dropdown")
                .html(
                    '<div class="dropdown-item text-danger">Error loading units</div>'
                )
                .show();
        },
    });
}

// Function to add job
function addJob() {
    const description = $("#job_description_input").val().trim();
    const highlight = $("#job_highlight_input").is(":checked");

    if (!description) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Deskripsi pekerjaan harus diisi",
        });
        return;
    }

    // Check for duplicates
    const exists = selectedJobs.some(
        (job) => job.description.toLowerCase() === description.toLowerCase()
    );
    if (exists) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Pekerjaan dengan deskripsi yang sama sudah ada",
        });
        return;
    }

    // Add to selected jobs
    selectedJobs.push({
        id: Date.now(), // Temporary ID for frontend
        description: description,
        highlight: highlight,
    });

    // Clear inputs
    $("#job_description_input").val("");
    $("#job_highlight_input").prop("checked", false);

    // Update display
    updateJobsList();
}

// Function to update jobs list display
function updateJobsList() {
    const container = $("#jobs_list");

    if (selectedJobs.length === 0) {
        container.html(
            '<small class="text-muted">Belum ada pekerjaan ditambahkan</small>'
        );
        return;
    }

    let html = "";
    selectedJobs.forEach(function (job) {
        const highlightClass = job.highlight ? "highlighted" : "";
        const highlightBadge = job.highlight
            ? '<span class="highlight-badge">Highlight</span>'
            : "";

        html += `
            <div class="job-item ${highlightClass}" data-job-id="${job.id}">
                <div class="job-info">
                    <span>${job.description}</span>
                    ${highlightBadge}
                </div>
                <button type="button" class="remove-btn" onclick="removeJob(${job.id})">×</button>
            </div>
        `;
    });

    container.html(html);
}

// Function to remove job
function removeJob(jobId) {
    selectedJobs = selectedJobs.filter((job) => job.id !== jobId);
    updateJobsList();
}

// Function to add technician
function addTechnician() {
    const name = $("#technician_name_input").val().trim();

    if (!name) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Nama teknisi harus diisi",
        });
        return;
    }

    // Check for duplicates
    const exists = selectedTechnicians.some(
        (tech) => tech.name.toLowerCase() === name.toLowerCase()
    );
    if (exists) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Teknisi dengan nama yang sama sudah ada",
        });
        return;
    }

    // Add to selected technicians
    selectedTechnicians.push({
        id: Date.now(), // Temporary ID for frontend
        name: name,
    });

    // Clear input
    $("#technician_name_input").val("");

    // Update display
    updateTechniciansList();
}

// Function to update technicians list display
function updateTechniciansList() {
    const container = $("#technicians_list");

    if (selectedTechnicians.length === 0) {
        container.html(
            '<small class="text-muted">Belum ada teknisi ditambahkan</small>'
        );
        return;
    }

    let html = "";
    selectedTechnicians.forEach(function (technician) {
        html += `
            <div class="technician-item" data-technician-id="${technician.id}">
                <span>${technician.name}</span>
                <button type="button" class="remove-btn" onclick="removeTechnician(${technician.id})">×</button>
            </div>
        `;
    });

    container.html(html);
}

// Function to remove technician
function removeTechnician(technicianId) {
    selectedTechnicians = selectedTechnicians.filter(
        (tech) => tech.id !== technicianId
    );
    updateTechniciansList();
}

// Function to render pagination
function renderPagination(response) {
    let paginationHtml = "";

    if (response.last_page > 1) {
        paginationHtml =
            '<nav aria-label="Page navigation"><ul class="pagination pagination-sm">';

        // Previous button
        if (response.current_page > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="?page=${
                response.current_page - 1
            }">Previous</a></li>`;
        }

        // Page numbers
        for (let i = 1; i <= response.last_page; i++) {
            if (i === response.current_page) {
                paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                paginationHtml += `<li class="page-item"><a class="page-link" href="?page=${i}">${i}</a></li>`;
            }
        }

        // Next button
        if (response.current_page < response.last_page) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="?page=${
                response.current_page + 1
            }">Next</a></li>`;
        }

        paginationHtml += "</ul></nav>";
    }

    $("#pagination-container").html(paginationHtml);
}

// Function to save daily report
function saveDailyReport() {
    const formData = new FormData($("#daily-report-form")[0]);
    const id = $("#daily-report-id").val();
    const url = id ? `/daily-reports/${id}` : "/daily-reports";
    const method = id ? "PUT" : "POST";

    // Add method override for PUT requests
    if (method === "PUT") {
        formData.append("_method", "PUT");
    }

    // Validate required fields
    const requiredFields = [
        "unit_id",
        "date_in",
        "hour_in",
        "hour_out",
        "shift",
    ];
    let isValid = true;

    requiredFields.forEach(function (field) {
        const value = $(`#${field}`).val();
        if (!value) {
            $(`#${field}`).addClass("is-invalid");
            isValid = false;
        } else {
            $(`#${field}`).removeClass("is-invalid");
        }
    });

    // Validate unit selection
    if (!selectedUnit || !$("#unit_id").val()) {
        $("#unit_search").addClass("is-invalid");
        isValid = false;
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Unit harus dipilih",
        });
        return;
    } else {
        $("#unit_search").removeClass("is-invalid");
    }

    // Validate jobs selection
    if (selectedJobs.length === 0) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Minimal satu pekerjaan harus ditambahkan",
        });
        return;
    }

    // Validate technicians selection
    if (selectedTechnicians.length === 0) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Minimal satu teknisi harus ditambahkan",
        });
        return;
    }

    if (!isValid) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Mohon lengkapi semua field yang wajib diisi",
        });
        return;
    }

    // Add jobs data to form
    selectedJobs.forEach(function (job, index) {
        formData.append(`jobs[${index}][description]`, job.description);
        formData.append(`jobs[${index}][highlight]`, job.highlight ? "1" : "0");
    });

    // Add technicians data to form
    selectedTechnicians.forEach(function (technician, index) {
        formData.append(`technicians[${index}][name]`, technician.name);
    });

    // Show loading
    $("#save-btn")
        .prop("disabled", true)
        .html('<i class="mdi mdi-loading mdi-spin"></i> Menyimpan...');

    $.ajax({
        url: url,
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            Swal.fire({
                icon: "success",
                title: "Berhasil",
                text: response.message,
            });
            hideForm();
            loadDailyReportsData();
        },
        error: function (xhr) {
            let errorMessage = "Terjadi kesalahan saat menyimpan data";

            if (xhr.status === 422) {
                const errors = xhr.responseJSON.errors;
                let errorList = "";
                Object.keys(errors).forEach(function (key) {
                    errorList += `• ${errors[key][0]}\n`;
                });
                errorMessage = errorList;
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                icon: "error",
                title: "Error",
                text: errorMessage,
            });
        },
        complete: function () {
            $("#save-btn")
                .prop("disabled", false)
                .html('<i class="mdi mdi-content-save"></i> Simpan');
        },
    });
}

// Function to view daily report details
function viewDailyReport(id) {
    // Show modal first with loading
    $("#view-daily-report-modal").modal("show");
    $("#detail-loading").show();
    $("#daily-report-details").children().not("#detail-loading").hide();

    $.ajax({
        url: `/daily-reports/${id}`,
        type: "GET",
        success: function (response) {
            $("#detail-loading").hide();
            renderDailyReportDetails(response);
            $("#daily-report-details").children().not("#detail-loading").show();
        },
        error: function (xhr) {
            $("#detail-loading").hide();
            $("#view-daily-report-modal").modal("hide");
            Swal.fire({
                icon: "error",
                title: "Error",
                text: "Gagal memuat detail daily report",
            });
        },
    });
}

// Make functions globally accessible
window.viewDailyReport = viewDailyReport;

// Function to render daily report details
function renderDailyReportDetails(data) {
    const dateIn = new Date(data.date_in).toLocaleDateString("id-ID");
    const jobs = data.jobs
        .map(
            (j) =>
                `<span class="badge bg-primary me-1">${j.job_description}</span>`
        )
        .join("");
    const technicians = data.technicians
        .map((t) => `<span class="badge bg-info me-1">${t.name}</span>`)
        .join("");

    let beforeImages = "";
    let afterImages = "";
    let unitImages = "";

    data.images.forEach(function (image) {
        const imageHtml = `
            <div class="col-md-4 mb-2">
                <img src="/assets/daily_reports/${image.image_path}" class="img-fluid rounded" style="max-height: 150px; object-fit: cover;">
            </div>
        `;

        if (image.type === "before") {
            beforeImages += imageHtml;
        } else if (image.type === "after") {
            afterImages += imageHtml;
        } else if (image.type === "unit") {
            unitImages += imageHtml;
        }
    });

    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>Informasi Unit</h6>
                <table class="table table-sm">
                    <tr><td><strong>Unit Code:</strong></td><td>${
                        data.unit ? data.unit.unit_code : "-"
                    }</td></tr>
                    <tr><td><strong>Unit Type:</strong></td><td>${
                        data.unit ? data.unit.unit_type : "-"
                    }</td></tr>

                    <tr><td><strong>HM:</strong></td><td>${
                        data.hm || "-"
                    }</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Informasi Waktu</h6>
                <table class="table table-sm">
                    <tr><td><strong>Tanggal:</strong></td><td>${dateIn}</td></tr>
                    <tr><td><strong>Jam Masuk:</strong></td><td>${
                        data.hour_in
                    }</td></tr>
                    <tr><td><strong>Jam Keluar:</strong></td><td>${
                        data.hour_out
                    }</td></tr>
                    <tr><td><strong>Shift:</strong></td><td><span class="badge ${
                        data.shift === "DAY" ? "bg-warning" : "bg-info"
                    }">${data.shift}</span></td></tr>
                </table>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>Problem</h6>
                <table class="table table-sm">
                    <tr><td><strong>Problem:</strong></td><td>${
                        data.problem || "-"
                    }</td></tr>
                    <tr><td><strong>Component:</strong></td><td>${
                        data.problem_component || "-"
                    }</td></tr>
                    <tr><td><strong>Description:</strong></td><td>${
                        data.problem_description || "-"
                    }</td></tr>
                </table>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                <h6>Pekerjaan</h6>
                <div>${jobs}</div>
            </div>
            <div class="col-md-6">
                <h6>Teknisi</h6>
                <div>${technicians}</div>
            </div>
        </div>
        
        ${
            beforeImages
                ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Gambar Sebelum</h6>
                    <div class="row">${beforeImages}</div>
                </div>
            </div>
        `
                : ""
        }
        
        ${
            afterImages
                ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Gambar Sesudah</h6>
                    <div class="row">${afterImages}</div>
                </div>
            </div>
        `
                : ""
        }

        ${
            unitImages
                ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Gambar Unit</h6>
                    <div class="row">${unitImages}</div>
                </div>
            </div>
        `
                : ""
        }
    `;

    $("#daily-report-details").html(detailsHtml);
}

// Function to edit daily report
function editDailyReport(id) {
    // Show edit modal
    $("#edit-daily-report-modal").modal("show");

    $.ajax({
        url: `/daily-reports/${id}`,
        type: "GET",
        success: function (response) {
            // Fill edit modal form with data
            $("#edit-modal-daily-report-id").val(response.daily_report_id);
            $("#edit_modal_hm").val(response.hm);
            $("#edit_modal_problem").val(response.problem);
            $("#edit_modal_problem_component").val(response.problem_component);
            $("#edit_modal_problem_description").val(
                response.problem_description
            );
            
            let date = new Date(response.date_in);
            let yyyy = date.getFullYear();
            let mm = String(date.getMonth() + 1).padStart(2, "0");
            let dd = String(date.getDate()).padStart(2, "0");
            let formattedDate = `${yyyy}-${mm}-${dd}`;

            $("#edit_modal_date_in").val(formattedDate);

            $("#edit_modal_hour_in").val(response.hour_in);
            $("#edit_modal_hour_out").val(response.hour_out);
            $("#edit_modal_shift").val(response.shift);

            // Set unit selection for edit modal
            if (response.unit) {
                editModalSelectedUnit = {
                    id: response.unit.id,
                    unit_code: response.unit.unit_code,
                    unit_type: response.unit.unit_type,
                };
                $("#edit_modal_unit_search").val(
                    `${editModalSelectedUnit.unit_code} - ${editModalSelectedUnit.unit_type}`
                );
                $("#edit_modal_unit_id").val(editModalSelectedUnit.id);
            }

            // Populate selected jobs for edit modal
            editModalSelectedJobs = [];
            response.jobs.forEach(function (job) {
                editModalSelectedJobs.push({
                    id: job.job_description_id,
                    description: job.job_description,
                    highlight: job.highlight,
                });
            });
            updateEditModalJobsList();

            // Populate selected technicians for edit modal
            editModalSelectedTechnicians = [];
            response.technicians.forEach(function (technician) {
                editModalSelectedTechnicians.push({
                    id: technician.technician_id,
                    name: technician.name,
                });
            });
            updateEditModalTechniciansList();

            // Show existing images in edit modal
            showEditModalExistingImages(response.images);
        },
        error: function (xhr) {
            $("#edit-daily-report-modal").modal("hide");
            Swal.fire({
                icon: "error",
                title: "Error",
                text: "Gagal memuat data daily report",
            });
        },
    });
}

// Make editDailyReport globally accessible
window.editDailyReport = editDailyReport;

// Function to show existing images
function showExistingImages(images) {
    $("#before-images-preview").empty();
    $("#after-images-preview").empty();

    images.forEach(function (image) {
        const imageHtml = `
            <div class="image-preview" data-image-id="${image.id}">
                <img src="/assets/daily_reports/${image.image_path}" alt="Image">
                <button type="button" class="remove-image" onclick="removeExistingImage(${image.id})">×</button>
            </div>
        `;

        if (image.type === "before") {
            $("#before-images-preview").append(imageHtml);
        } else {
            $("#after-images-preview").append(imageHtml);
        }
    });
}

// Function to remove existing image
function removeExistingImage(imageId) {
    Swal.fire({
        title: "Konfirmasi",
        text: "Apakah Anda yakin ingin menghapus gambar ini?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "Ya, Hapus!",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/daily-reports/images/${imageId}`,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    $(`[data-image-id="${imageId}"]`).remove();
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil",
                        text: response.message,
                    });
                },
                error: function (xhr) {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Gagal menghapus gambar",
                    });
                },
            });
        }
    });
}

// Function to delete daily report
function deleteDailyReport(id) {
    Swal.fire({
        title: "Konfirmasi",
        text: "Apakah Anda yakin ingin menghapus daily report ini?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "Ya, Hapus!",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `/daily-reports/${id}`,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil",
                        text: response.message,
                    });
                    loadDailyReportsData();
                },
                error: function (xhr) {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Gagal menghapus daily report",
                    });
                },
            });
        }
    });
}

// Make deleteDailyReport globally accessible
window.deleteDailyReport = deleteDailyReport;

// Make other functions globally accessible for onclick handlers
window.removeJob = removeJob;
window.removeTechnician = removeTechnician;
window.removeModalJob = removeModalJob;
window.removeModalTechnician = removeModalTechnician;
window.removeExistingImage = removeExistingImage;
window.removePreviewImage = removePreviewImage;

// Function to save new job
function saveJob() {
    const formData = {
        job_description: $("#job_description").val(),
        highlight: $("#highlight").is(":checked"),
    };

    if (!formData.job_description) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Deskripsi pekerjaan harus diisi",
        });
        return;
    }

    $.ajax({
        url: "/daily-reports/jobs",
        type: "POST",
        data: formData,
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            // Add new job to the list
            const highlightClass = response.data.highlight
                ? "fw-bold text-warning"
                : "";
            const highlightIcon = response.data.highlight
                ? '<i class="mdi mdi-star text-warning"></i>'
                : "";

            const jobHtml = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="jobs[]" value="${response.data.job_description_id}" id="job_${response.data.job_description_id}" checked>
                    <label class="form-check-label ${highlightClass}" for="job_${response.data.job_description_id}">
                        ${response.data.job_description}
                        ${highlightIcon}
                    </label>
                </div>
            `;

            $("#jobs-container").append(jobHtml);
            $("#add-job-modal").modal("hide");

            Swal.fire({
                icon: "success",
                title: "Berhasil",
                text: response.message,
            });
        },
        error: function (xhr) {
            let errorMessage = "Terjadi kesalahan saat menyimpan pekerjaan";

            if (xhr.status === 422) {
                const errors = xhr.responseJSON.errors;
                errorMessage = Object.values(errors)[0][0];
            }

            Swal.fire({
                icon: "error",
                title: "Error",
                text: errorMessage,
            });
        },
    });
}

// Function to save new technician
function saveTechnician() {
    const formData = {
        name: $("#technician_name").val(),
    };

    if (!formData.name) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Nama teknisi harus diisi",
        });
        return;
    }

    $.ajax({
        url: "/daily-reports/technicians",
        type: "POST",
        data: formData,
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            // Add new technician to the list
            const technicianHtml = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="technicians[]" value="${response.data.technician_id}" id="technician_${response.data.technician_id}" checked>
                    <label class="form-check-label" for="technician_${response.data.technician_id}">
                        ${response.data.name}
                    </label>
                </div>
            `;

            $("#technicians-container").append(technicianHtml);
            $("#add-technician-modal").modal("hide");

            Swal.fire({
                icon: "success",
                title: "Berhasil",
                text: response.message,
            });
        },
        error: function (xhr) {
            let errorMessage = "Terjadi kesalahan saat menyimpan teknisi";

            if (xhr.status === 422) {
                const errors = xhr.responseJSON.errors;
                errorMessage = Object.values(errors)[0][0];
            }

            Swal.fire({
                icon: "error",
                title: "Error",
                text: errorMessage,
            });
        },
    });
}

// Function to preview images
function previewImages(input, container) {
    $(container).empty();

    if (input.files) {
        Array.from(input.files).forEach(function (file, index) {
            if (file.type.startsWith("image/")) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const imageHtml = `
                        <div class="image-preview" data-file-index="${index}">
                            <img src="${e.target.result}" alt="Preview">
                            <button type="button" class="remove-image" onclick="removePreviewImage(this, '${input.id}', ${index})">×</button>
                        </div>
                    `;
                    $(container).append(imageHtml);
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Function to remove preview image
function removePreviewImage(button, inputId, fileIndex) {
    const input = document.getElementById(inputId);
    const dt = new DataTransfer();

    // Add all files except the one to remove
    Array.from(input.files).forEach(function (file, index) {
        if (index !== fileIndex) {
            dt.items.add(file);
        }
    });

    input.files = dt.files;
    $(button).closest(".image-preview").remove();
}

// Modal-specific functions
function resetModalForm() {
    $("#modal-daily-report-form")[0].reset();
    $("#modal-daily-report-id").val("");
    $("#modal-before-images-preview").empty();
    $("#modal-after-images-preview").empty();
    $("#modal-unit-images-preview").empty();

    // Reset unit selection
    modalSelectedUnit = null;
    $("#modal_unit_search").val("");
    $("#modal_unit_id").val("");
    $("#modal_unit_dropdown").hide();

    // Reset jobs and technicians
    modalSelectedJobs = [];
    modalSelectedTechnicians = [];
    updateModalJobsList();
    updateModalTechniciansList();

    // Set default date
    $("#modal_date_in").val(new Date().toISOString().split("T")[0]);
}

function searchModalUnits(search) {
    $.ajax({
        url: "/daily-reports/units/search",
        type: "GET",
        data: { search: search },
        success: function (units) {
            let html = "";
            if (units.length === 0) {
                html =
                    '<div class="dropdown-item text-muted">Tidak ada unit ditemukan</div>';
            } else {
                units.forEach(function (unit) {
                    html += `
                        <div class="dropdown-item" data-unit-id="${unit.id}" data-unit-code="${unit.unit_code}" data-unit-type="${unit.unit_type}">
                            <strong>${unit.unit_code}</strong> - ${unit.unit_type}
                        </div>
                    `;
                });
            }
            $("#modal_unit_dropdown").html(html).show();

            // Handle unit selection
            $("#modal_unit_dropdown .dropdown-item").click(function () {
                if ($(this).data("unit-id")) {
                    modalSelectedUnit = {
                        id: $(this).data("unit-id"),
                        unit_code: $(this).data("unit-code"),
                        unit_type: $(this).data("unit-type"),
                    };
                    $("#modal_unit_search").val(
                        `${modalSelectedUnit.unit_code} - ${modalSelectedUnit.unit_type}`
                    );
                    $("#modal_unit_id").val(modalSelectedUnit.id);
                    $("#modal_unit_dropdown").hide();
                }
            });
        },
        error: function () {
            $("#modal_unit_dropdown")
                .html(
                    '<div class="dropdown-item text-danger">Error loading units</div>'
                )
                .show();
        },
    });
}

function addModalJob() {
    const description = $("#modal_job_description_input").val().trim();
    const highlight = $("#modal_job_highlight_input").is(":checked");

    if (!description) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Deskripsi pekerjaan harus diisi",
        });
        return;
    }

    // Check for duplicates
    const exists = modalSelectedJobs.some(
        (job) => job.description.toLowerCase() === description.toLowerCase()
    );
    if (exists) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Pekerjaan dengan deskripsi yang sama sudah ada",
        });
        return;
    }

    // Add to selected jobs
    modalSelectedJobs.push({
        id: Date.now(), // Temporary ID for frontend
        description: description,
        highlight: highlight,
    });

    // Clear inputs
    $("#modal_job_description_input").val("");
    $("#modal_job_highlight_input").prop("checked", false);

    // Update display
    updateModalJobsList();
}

function updateModalJobsList() {
    const container = $("#modal_jobs_list");

    if (modalSelectedJobs.length === 0) {
        container.html(
            '<small class="text-muted">Belum ada pekerjaan ditambahkan</small>'
        );
        return;
    }

    let html = "";
    modalSelectedJobs.forEach(function (job) {
        const highlightClass = job.highlight ? "highlighted" : "";
        const highlightBadge = job.highlight
            ? '<span class="highlight-badge">Highlight</span>'
            : "";

        html += `
            <div class="job-item ${highlightClass}" data-job-id="${job.id}">
                <div class="job-info">
                    <span>${job.description}</span>
                    ${highlightBadge}
                </div>
                <button type="button" class="remove-btn" onclick="removeModalJob(${job.id})">×</button>
            </div>
        `;
    });

    container.html(html);
}

function removeModalJob(jobId) {
    modalSelectedJobs = modalSelectedJobs.filter((job) => job.id !== jobId);
    updateModalJobsList();
}

function addModalTechnician() {
    const name = $("#modal_technician_name_input").val().trim();

    if (!name) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Nama teknisi harus diisi",
        });
        return;
    }

    // Check for duplicates
    const exists = modalSelectedTechnicians.some(
        (tech) => tech.name.toLowerCase() === name.toLowerCase()
    );
    if (exists) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Teknisi dengan nama yang sama sudah ada",
        });
        return;
    }

    // Add to selected technicians
    modalSelectedTechnicians.push({
        id: Date.now(), // Temporary ID for frontend
        name: name,
    });

    // Clear input
    $("#modal_technician_name_input").val("");

    // Update display
    updateModalTechniciansList();
}

function updateModalTechniciansList() {
    const container = $("#modal_technicians_list");

    if (modalSelectedTechnicians.length === 0) {
        container.html(
            '<small class="text-muted">Belum ada teknisi ditambahkan</small>'
        );
        return;
    }

    let html = "";
    modalSelectedTechnicians.forEach(function (technician) {
        html += `
            <div class="technician-item" data-technician-id="${technician.id}">
                <span>${technician.name}</span>
                <button type="button" class="remove-btn" onclick="removeModalTechnician(${technician.id})">×</button>
            </div>
        `;
    });

    container.html(html);
}

function removeModalTechnician(technicianId) {
    modalSelectedTechnicians = modalSelectedTechnicians.filter(
        (tech) => tech.id !== technicianId
    );
    updateModalTechniciansList();
}

function saveModalDailyReport() {
    const formData = new FormData($("#modal-daily-report-form")[0]);
    const id = $("#modal-daily-report-id").val();
    const url = id ? `/daily-reports/${id}` : "/daily-reports";
    const method = id ? "PUT" : "POST";

    // Add method override for PUT requests
    if (method === "PUT") {
        formData.append("_method", "PUT");
    }

    // Validate required fields
    const requiredFields = [
        "modal_unit_id",
        "modal_date_in",
        "modal_hour_in",
        "modal_hour_out",
        "modal_shift",
    ];
    let isValid = true;

    requiredFields.forEach(function (field) {
        const value = $(`#${field}`).val();
        if (!value) {
            $(`#${field}`).addClass("is-invalid");
            isValid = false;
        } else {
            $(`#${field}`).removeClass("is-invalid");
        }
    });

    // Validate unit selection
    if (!modalSelectedUnit || !$("#modal_unit_id").val()) {
        $("#modal_unit_search").addClass("is-invalid");
        isValid = false;
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Unit harus dipilih",
        });
        return;
    } else {
        $("#modal_unit_search").removeClass("is-invalid");
    }

    // Validate jobs selection
    if (modalSelectedJobs.length === 0) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Minimal satu pekerjaan harus ditambahkan",
        });
        return;
    }

    // Validate technicians selection
    if (modalSelectedTechnicians.length === 0) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Minimal satu teknisi harus ditambahkan",
        });
        return;
    }

    if (!isValid) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Mohon lengkapi semua field yang wajib diisi",
        });
        return;
    }

    // Add jobs data to form
    modalSelectedJobs.forEach(function (job, index) {
        formData.append(`jobs[${index}][description]`, job.description);
        formData.append(`jobs[${index}][highlight]`, job.highlight ? "1" : "0");
    });

    // Add technicians data to form
    modalSelectedTechnicians.forEach(function (technician, index) {
        formData.append(`technicians[${index}][name]`, technician.name);
    });

    // Show loading
    $("#modal-loading").removeClass("d-none");
    $("#modal-save-btn")
        .prop("disabled", true)
        .html('<i class="mdi mdi-loading mdi-spin"></i> Menyimpan...');

    $.ajax({
        url: url,
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            $("#modal-loading").addClass("d-none");
            Swal.fire({
                icon: "success",
                title: "Berhasil",
                text: response.message,
            });
            $("#add-daily-report-modal").modal("hide");
            loadDailyReportsData();
        },
        error: function (xhr) {
            $("#modal-loading").addClass("d-none");
            let errorMessage = "Terjadi kesalahan saat menyimpan data";

            if (xhr.status === 422) {
                const errors = xhr.responseJSON.errors;
                let errorList = "";
                Object.keys(errors).forEach(function (key) {
                    errorList += `• ${errors[key][0]}\n`;
                });
                errorMessage = errorList;
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                icon: "error",
                title: "Error",
                text: errorMessage,
            });
        },
        complete: function () {
            $("#modal-save-btn")
                .prop("disabled", false)
                .html('<i class="mdi mdi-content-save"></i> Simpan');
        },
    });
}

// Edit Modal Functions
function searchEditModalUnits(search) {
    $.ajax({
        url: "/daily-reports/units/search",
        type: "GET",
        data: { search: search },
        success: function (units) {
            let html = "";
            if (units.length === 0) {
                html =
                    '<div class="dropdown-item text-muted">Tidak ada unit ditemukan</div>';
            } else {
                units.forEach(function (unit) {
                    html += `
                        <div class="dropdown-item" data-unit-id="${unit.id}" data-unit-code="${unit.unit_code}" data-unit-type="${unit.unit_type}">
                            <strong>${unit.unit_code}</strong> - ${unit.unit_type}
                        </div>
                    `;
                });
            }
            $("#edit_modal_unit_dropdown").html(html).show();

            // Handle unit selection
            $("#edit_modal_unit_dropdown .dropdown-item").click(function () {
                if ($(this).data("unit-id")) {
                    editModalSelectedUnit = {
                        id: $(this).data("unit-id"),
                        unit_code: $(this).data("unit-code"),
                        unit_type: $(this).data("unit-type"),
                    };
                    $("#edit_modal_unit_search").val(
                        `${editModalSelectedUnit.unit_code} - ${editModalSelectedUnit.unit_type}`
                    );
                    $("#edit_modal_unit_id").val(editModalSelectedUnit.id);
                    $("#edit_modal_unit_dropdown").hide();
                }
            });
        },
        error: function () {
            $("#edit_modal_unit_dropdown")
                .html(
                    '<div class="dropdown-item text-danger">Error loading units</div>'
                )
                .show();
        },
    });
}

function addEditModalJob() {
    const description = $("#edit_modal_job_description_input").val().trim();
    const highlight = $("#edit_modal_job_highlight_input").is(":checked");

    if (!description) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Deskripsi pekerjaan harus diisi",
        });
        return;
    }

    // Check for duplicates
    const exists = editModalSelectedJobs.some(
        (job) => job.description.toLowerCase() === description.toLowerCase()
    );
    if (exists) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Pekerjaan dengan deskripsi yang sama sudah ada",
        });
        return;
    }

    // Add to selected jobs
    editModalSelectedJobs.push({
        id: Date.now(), // Temporary ID for frontend
        description: description,
        highlight: highlight,
    });

    // Clear inputs
    $("#edit_modal_job_description_input").val("");
    $("#edit_modal_job_highlight_input").prop("checked", false);

    // Update display
    updateEditModalJobsList();
}

function updateEditModalJobsList() {
    const container = $("#edit_modal_jobs_list");

    if (editModalSelectedJobs.length === 0) {
        container.html(
            '<small class="text-muted">Belum ada pekerjaan ditambahkan</small>'
        );
        return;
    }

    let html = "";
    editModalSelectedJobs.forEach(function (job) {
        const highlightClass = job.highlight ? "highlighted" : "";
        const highlightBadge = job.highlight
            ? '<span class="highlight-badge">Highlight</span>'
            : "";

        html += `
            <div class="job-item ${highlightClass}" data-job-id="${job.id}">
                <div class="job-info">
                    <span>${job.description}</span>
                    ${highlightBadge}
                </div>
                <button type="button" class="remove-btn" onclick="removeEditModalJob(${job.id})">×</button>
            </div>
        `;
    });

    container.html(html);
}

function removeEditModalJob(jobId) {
    editModalSelectedJobs = editModalSelectedJobs.filter(
        (job) => job.id !== jobId
    );
    updateEditModalJobsList();
}

function addEditModalTechnician() {
    const name = $("#edit_modal_technician_name_input").val().trim();

    if (!name) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Nama teknisi harus diisi",
        });
        return;
    }

    // Check for duplicates
    const exists = editModalSelectedTechnicians.some(
        (tech) => tech.name.toLowerCase() === name.toLowerCase()
    );
    if (exists) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Teknisi dengan nama yang sama sudah ada",
        });
        return;
    }

    // Add to selected technicians
    editModalSelectedTechnicians.push({
        id: Date.now(), // Temporary ID for frontend
        name: name,
    });

    // Clear input
    $("#edit_modal_technician_name_input").val("");

    // Update display
    updateEditModalTechniciansList();
}

function updateEditModalTechniciansList() {
    const container = $("#edit_modal_technicians_list");

    if (editModalSelectedTechnicians.length === 0) {
        container.html(
            '<small class="text-muted">Belum ada teknisi ditambahkan</small>'
        );
        return;
    }

    let html = "";
    editModalSelectedTechnicians.forEach(function (technician) {
        html += `
            <div class="technician-item" data-technician-id="${technician.id}">
                <span>${technician.name}</span>
                <button type="button" class="remove-btn" onclick="removeEditModalTechnician(${technician.id})">×</button>
            </div>
        `;
    });

    container.html(html);
}

function removeEditModalTechnician(technicianId) {
    editModalSelectedTechnicians = editModalSelectedTechnicians.filter(
        (tech) => tech.id !== technicianId
    );
    updateEditModalTechniciansList();
}

function showEditModalExistingImages(images) {
    $("#edit-existing-before-images").empty();
    $("#edit-existing-after-images").empty();
    $("#edit-existing-unit-images").empty();

    images.forEach(function (image) {
        const imageHtml = `
            <div class="image-preview" data-image-id="${image.id}">
                <img src="/assets/daily_reports/${image.image_path}" alt="Image">
                <button type="button" class="remove-image" onclick="removeExistingImage(${image.id})">×</button>
            </div>
        `;

        if (image.type === "before") {
            $("#edit-existing-before-images").append(imageHtml);
        } else if (image.type === "after") {
            $("#edit-existing-after-images").append(imageHtml);
        } else if (image.type === "unit") {
            $("#edit-existing-unit-images").append(imageHtml);
        }
    });
}

function saveEditModalDailyReport() {
    const formData = new FormData($("#edit-modal-daily-report-form")[0]);
    const id = $("#edit-modal-daily-report-id").val();
    const url = `/daily-reports/${id}`;

    // Add method override for PUT requests
    formData.append("_method", "PUT");

    // Validate required fields
    const requiredFields = [
        "edit_modal_unit_id",
        "edit_modal_date_in",
        "edit_modal_hour_in",
        "edit_modal_hour_out",
        "edit_modal_shift",
    ];
    let isValid = true;

    requiredFields.forEach(function (field) {
        const value = $(`#${field}`).val();
        if (!value) {
            $(`#${field}`).addClass("is-invalid");
            isValid = false;
        } else {
            $(`#${field}`).removeClass("is-invalid");
        }
    });

    // Validate unit selection
    if (!editModalSelectedUnit || !$("#edit_modal_unit_id").val()) {
        $("#edit_modal_unit_search").addClass("is-invalid");
        isValid = false;
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Unit harus dipilih",
        });
        return;
    } else {
        $("#edit_modal_unit_search").removeClass("is-invalid");
    }

    // Validate jobs selection
    if (editModalSelectedJobs.length === 0) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Minimal satu pekerjaan harus ditambahkan",
        });
        return;
    }

    // Validate technicians selection
    if (editModalSelectedTechnicians.length === 0) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Minimal satu teknisi harus ditambahkan",
        });
        return;
    }

    if (!isValid) {
        Swal.fire({
            icon: "warning",
            title: "Peringatan",
            text: "Mohon lengkapi semua field yang wajib diisi",
        });
        return;
    }

    // Add jobs data to form
    editModalSelectedJobs.forEach(function (job, index) {
        formData.append(`jobs[${index}][description]`, job.description);
        formData.append(`jobs[${index}][highlight]`, job.highlight ? "1" : "0");
    });

    // Add technicians data to form
    editModalSelectedTechnicians.forEach(function (technician, index) {
        formData.append(`technicians[${index}][name]`, technician.name);
    });

    // Show loading
    $("#edit-modal-loading").removeClass("d-none");
    $("#edit-modal-save-btn")
        .prop("disabled", true)
        .html('<i class="mdi mdi-loading mdi-spin"></i> Menyimpan...');

    $.ajax({
        url: url,
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            $("#edit-modal-loading").addClass("d-none");
            Swal.fire({
                icon: "success",
                title: "Berhasil",
                text: response.message,
            });
            $("#edit-daily-report-modal").modal("hide");
            loadDailyReportsData();
        },
        error: function (xhr) {
            $("#edit-modal-loading").addClass("d-none");
            let errorMessage = "Terjadi kesalahan saat menyimpan data";

            if (xhr.status === 422) {
                const errors = xhr.responseJSON.errors;
                let errorList = "";
                Object.keys(errors).forEach(function (key) {
                    errorList += `• ${errors[key][0]}\n`;
                });
                errorMessage = errorList;
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            Swal.fire({
                icon: "error",
                title: "Error",
                text: errorMessage,
            });
        },
        complete: function () {
            $("#edit-modal-save-btn")
                .prop("disabled", false)
                .html('<i class="mdi mdi-content-save"></i> Update');
        },
    });
}

// Export functions
function exportToPdf() {
    const search = $("#search-input").val();
    const startDate = $("#start-date").val();
    const endDate = $("#end-date").val();
    const shift = $("#shift-filter").val();

    // Build query parameters
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
    });

    if (search) params.append("search", search);
    if (shift) params.append("shift", shift);

    // Create download link
    const url = `/daily-reports/export/pdf?${params.toString()}`;
    window.open(url, "_blank");
}

function exportToExcel() {
    const search = $("#search-input").val();
    const startDate = $("#start-date").val();
    const endDate = $("#end-date").val();
    const shift = $("#shift-filter").val();

    // Build query parameters
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
    });

    if (search) params.append("search", search);
    if (shift) params.append("shift", shift);

    // Create download link
    const url = `/daily-reports/export/excel?${params.toString()}`;
    window.location.href = url;
}

// Make edit modal functions globally accessible
window.removeEditModalJob = removeEditModalJob;
window.removeEditModalTechnician = removeEditModalTechnician;
