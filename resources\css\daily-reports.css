.skeleton-loader {
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-row {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.image-preview {
    position: relative;
    display: inline-block;
    margin: 5px;
}

.image-preview img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.image-preview .remove-image {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
}

/* Unit dropdown styles */
#unit_dropdown, #modal_unit_dropdown, #edit_modal_unit_dropdown {
    z-index: 1050;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#unit_dropdown .dropdown-item, #modal_unit_dropdown .dropdown-item, #edit_modal_unit_dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

#unit_dropdown .dropdown-item:hover, #modal_unit_dropdown .dropdown-item:hover, #edit_modal_unit_dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
}

#unit_dropdown .dropdown-item:last-child, #modal_unit_dropdown .dropdown-item:last-child, #edit_modal_unit_dropdown .dropdown-item:last-child {
    border-bottom: none;
}

/* Job and technician list item styles */
.job-item, .technician-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.job-item.highlighted {
    border-color: #225297;
    background-color: #225297;
    color: white;
}

/* Highlighted text only - no table row colors */
.highlighted-job-text {
    background-color: #225297;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
}

.job-item .job-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.job-item .highlight-badge {
    color: #ffc107;
    font-weight: bold;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn:hover {
    background: #c82333;
}


#add-daily-report-modal .modal-dialog.modal-fullscreen,
#edit-daily-report-modal .modal-dialog.modal-fullscreen {
    width: 100vw;       /* Full viewport width */
    max-width: none;    /* Remove any max-width constraint */
    height: 100vh;      /* Full viewport height */
    margin: 0;          /* Remove any margins */
    padding: 0;         /* Remove any padding around the dialog */
}

/* Ensure the modal content fills the dialog and has no borders/radius */
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-content,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-content {
    height: 100%;       /* Content fills the 100vh dialog */
    border-radius: 0;   /* No rounded corners for a fullscreen experience */
    border: 0;          /* No border for a fullscreen experience */
}

/* Ensure the modal body can scroll if its content overflows */
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-body,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-body {
    overflow-y: auto;
}

/* Optional: Ensure header and footer also don't have rounded corners
   Bootstrap's modal-fullscreen usually handles this, but just in case. */
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-header,
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-footer,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-header,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-footer {
    border-radius: 0;
}

