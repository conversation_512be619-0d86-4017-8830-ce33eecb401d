<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>TAR - Daily Reports</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            font-weight: normal;
        }
        .period {
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .report-item {
            margin-bottom: 25px;
            border: 1px solid #ccc;
            padding: 15px;
            page-break-inside: avoid;
        }
        .report-header {
            background-color: #f5f5f5;
            padding: 10px;
            margin: -15px -15px 15px -15px;
            border-bottom: 1px solid #ccc;
        }
        .report-header h3 {
            margin: 0;
            font-size: 14px;
        }
        .info-grid {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        .info-row {
            display: table-row;
        }
        .info-label {
            display: table-cell;
            width: 150px;
            font-weight: bold;
            padding: 3px 0;
            vertical-align: top;
        }
        .info-value {
            display: table-cell;
            padding: 3px 0;
            vertical-align: top;
        }
        .jobs-section, .technicians-section {
            margin-top: 15px;
        }
        .section-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .jobs-list, .technicians-list {
            margin-left: 20px;
        }
        .job-item {
            margin-bottom: 3px;
        }
        .highlighted-job {
            background-color: #225297;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .images-section {
            margin-top: 15px;
        }
        .images-grid {
            display: table;
            width: 100%;
        }
        .images-column {
            display: table-cell;
            width: 33.33%;
            vertical-align: top;
            padding-right: 10px;
        }
        .image-item {
            margin-bottom: 10px;
            text-align: center;
        }
        .image-item img {
            max-width: 100%;
            max-height: 150px;
            border: 1px solid #ddd;
        }
        .image-caption {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        .page-break {
            page-break-before: always;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TAR (Technical Activity Report)</h1>
        <h2>Daily Reports</h2>
    </div>

    <div class="period">
        Periode: <?php echo e(\Carbon\Carbon::parse($startDate)->format('d/m/Y')); ?> - <?php echo e(\Carbon\Carbon::parse($endDate)->format('d/m/Y')); ?>

    </div>

    <?php $__currentLoopData = $dailyReports; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $report): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($index > 0): ?>
            <div class="page-break"></div>
        <?php endif; ?>
        
        <div class="report-item">
            <div class="report-header">
                <h3><?php echo e($report->unit->unit_code ?? 'N/A'); ?> - <?php echo e($report->unit->unit_type ?? 'N/A'); ?></h3>
            </div>

            <div class="info-grid">
                <div class="info-row">
                    <div class="info-label">Tanggal:</div>
                    <div class="info-value"><?php echo e($report->date_in->format('d/m/Y')); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Jam Kerja:</div>
                    <div class="info-value"><?php echo e($report->hour_in); ?> - <?php echo e($report->hour_out); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Shift:</div>
                    <div class="info-value"><?php echo e($report->shift); ?></div>
                </div>
                <?php if($report->hm): ?>
                <div class="info-row">
                    <div class="info-label">HM:</div>
                    <div class="info-value"><?php echo e($report->hm); ?></div>
                </div>
                <?php endif; ?>
                <?php if($report->problem): ?>
                <div class="info-row">
                    <div class="info-label">Problem:</div>
                    <div class="info-value"><?php echo e($report->problem); ?></div>
                </div>
                <?php endif; ?>
                <?php if($report->problem_component): ?>
                <div class="info-row">
                    <div class="info-label">Problem Component:</div>
                    <div class="info-value"><?php echo e($report->problem_component); ?></div>
                </div>
                <?php endif; ?>
                <?php if($report->problem_description): ?>
                <div class="info-row">
                    <div class="info-label">Problem Description:</div>
                    <div class="info-value"><?php echo e($report->problem_description); ?></div>
                </div>
                <?php endif; ?>
            </div>

            <?php if($report->jobs->count() > 0): ?>
            <div class="jobs-section">
                <div class="section-title">Pekerjaan yang Dilakukan:</div>
                <div class="jobs-list">
                    <?php $__currentLoopData = $report->jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="job-item">
                        <?php if($job->highlight): ?>
                            <span class="highlighted-job"><?php echo e($job->job_description); ?></span>
                        <?php else: ?>
                            • <?php echo e($job->job_description); ?>

                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if($report->technicians->count() > 0): ?>
            <div class="technicians-section">
                <div class="section-title">Teknisi:</div>
                <div class="technicians-list">
                    <?php $__currentLoopData = $report->technicians; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $technician): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div>• <?php echo e($technician->name); ?></div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if($report->images->count() > 0): ?>
            <div class="images-section">
                <div class="section-title">Dokumentasi:</div>
                <div class="images-grid">
                    <div class="images-column">
                        <strong>Gambar Sebelum:</strong>
                        <?php $__currentLoopData = $report->images->where('type', 'before'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="image-item">
                            <img src="<?php echo e(public_path('assets/daily_reports/' . $image->image_path)); ?>" alt="Before Image">
                            <div class="image-caption"><?php echo e($image->image_path); ?></div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="images-column">
                        <strong>Gambar Sesudah:</strong>
                        <?php $__currentLoopData = $report->images->where('type', 'after'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="image-item">
                            <img src="<?php echo e(public_path('assets/daily_reports/' . $image->image_path)); ?>" alt="After Image">
                            <div class="image-caption"><?php echo e($image->image_path); ?></div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="images-column">
                        <strong>Gambar Unit:</strong>
                        <?php $__currentLoopData = $report->images->where('type', 'unit'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="image-item">
                            <img src="<?php echo e(public_path('assets/daily_reports/' . $image->image_path)); ?>" alt="Unit Image">
                            <div class="image-caption"><?php echo e($image->image_path); ?></div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <div class="footer">
        Generated on <?php echo e(now()->format('d/m/Y H:i:s')); ?>

    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/daily-reports/pdf.blade.php ENDPATH**/ ?>