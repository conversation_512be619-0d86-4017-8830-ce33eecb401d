<?php $__env->startSection('title', 'Daily Reports'); ?>
<?php $__env->startSection('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/daily-reports.js']); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/css/daily-reports.css']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsite'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('sites.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Daily Reports</li>
                    </ol>
                </div>
                <h4 class="page-title">Daily Reports</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Data Table Section (Full Width) -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h4 class="header-title">Daftar Daily Reports</h4>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success" id="export-excel-btn">
                                    <i class="mdi mdi-file-excel me-1"></i> Export Excel
                                </button>
                                <button type="button" class="btn btn-danger" id="export-pdf-btn">
                                    <i class="mdi mdi-file-pdf me-1"></i> Print TAR
                                </button>
                                <button type="button" class="btn btn-primary" id="add-daily-report-btn" data-bs-toggle="modal" data-bs-target="#add-daily-report-modal">
                                    <i class="mdi mdi-plus-circle me-1"></i> Add New Report
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="search-input" placeholder="Cari...">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="start-date" placeholder="Tanggal Mulai">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="end-date" placeholder="Tanggal Akhir">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="shift-filter">
                                <option value="">Semua Shift</option>
                                <option value="DAY">DAY</option>
                                <option value="NIGHT">NIGHT</option>
                            </select>
                        </div>
                    </div>

                    <!-- Loading Skeleton -->
                    <div id="loading-skeleton" class="d-none">
                        <div class="skeleton-loader">
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="daily-reports-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>Unit Code</th>
                                    <th>Tanggal</th>
                                    <th>Shift</th>
                                    <th>Problem</th>
                                    <th>Teknisi</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="daily-reports-table-body">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div id="pagination-container" class="d-flex justify-content-center mt-3">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>



<!-- View Daily Report Modal -->
<div class="modal fade" id="view-daily-report-modal" tabindex="-1" aria-labelledby="view-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="view-daily-report-modal-label">Detail Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="daily-report-details">
                <!-- Loading spinner -->
                <div id="detail-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Memuat detail laporan...</p>
                </div>
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Add New Daily Report Modal (Full Screen) -->
<div class="modal fade" id="add-daily-report-modal" tabindex="-1" aria-labelledby="add-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="add-daily-report-modal-label">Add New Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner for modal -->
                <div id="modal-loading" class="text-center py-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Menyimpan data...</p>
                </div>

                <!-- Table-like Form Layout -->
                <div class="table-responsive">
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td colspan="2" class="bg-light">
                                    <h6 class="mb-0 text-primary">Informasi Daily Report</h6>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 50%;">
                                    <form id="modal-daily-report-form" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" id="modal-daily-report-id" name="id">

                                        <!-- Unit Auto-Search -->
                                        <div class="mb-3">
                                            <label class="form-label">Unit <span class="text-danger">*</span></label>
                                            <div class="position-relative">
                                                <input type="text" class="form-control" id="modal_unit_search" placeholder="Cari unit berdasarkan kode atau tipe..." autocomplete="off">
                                                <input type="hidden" id="modal_unit_id" name="unit_id" required>
                                                <div id="modal_unit_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                                    <!-- Search results will appear here -->
                                                </div>
                                            </div>
                                            <small class="text-muted">Ketik minimal 2 karakter untuk mencari unit</small>
                                        </div>

                                        <!-- HM -->
                                        <div class="mb-3">
                                            <label class="form-label">HM</label>
                                            <input type="number" class="form-control" id="modal_hm" name="hm" step="0.01" min="0" placeholder="Hour Meter">
                                        </div>

                                        <!-- Problem Fields -->
                                        <div class="mb-3">
                                            <label class="form-label">Problem</label>
                                            <input type="text" class="form-control" id="modal_problem" name="problem" placeholder="Masalah yang ditemukan">
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Problem Component</label>
                                            <input type="text" class="form-control" id="modal_problem_component" name="problem_component" placeholder="Komponen bermasalah">
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Problem Description</label>
                                            <textarea class="form-control" id="modal_problem_description" name="problem_description" rows="3" placeholder="Deskripsi detail masalah"></textarea>
                                        </div>

                                        <!-- Date and Time -->
                                        <div class="mb-3">
                                            <label for="modal_date_in" class="form-label">Tanggal <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="modal_date_in" name="date_in" required>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="modal_hour_in" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                                                    <input type="time" class="form-control" id="modal_hour_in" name="hour_in" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="modal_hour_out" class="form-label">Jam Keluar <span class="text-danger">*</span></label>
                                                    <input type="time" class="form-control" id="modal_hour_out" name="hour_out" required>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Shift -->
                                        <div class="mb-3">
                                            <label for="modal_shift" class="form-label">Shift <span class="text-danger">*</span></label>
                                            <select class="form-control" id="modal_shift" name="shift" required>
                                                <option value="">Pilih Shift</option>
                                                <option value="DAY">DAY</option>
                                                <option value="NIGHT">NIGHT</option>
                                            </select>
                                        </div>
                                </td>
                                    <!-- Jobs Management -->
                                    <div class="mb-3">
                                        <label class="form-label">Pekerjaan <span class="text-danger">*</span></label>

                                        <!-- Inline Job Creation Form -->
                                        <div class="border rounded p-3 mb-3 bg-light">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <input type="text" class="form-control" id="modal_job_description_input" placeholder="Deskripsi pekerjaan...">
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="modal_job_highlight_input">
                                                        <label class="form-check-label" for="modal_job_highlight_input">
                                                            Highlight
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-primary btn-sm w-100" id="modal_add_job_btn">
                                                        <i class="mdi mdi-plus"></i> Tambah
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Jobs List -->
                                        <div id="modal_jobs_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                            <small class="text-muted">Belum ada pekerjaan ditambahkan</small>
                                        </div>
                                        <small class="text-muted">Minimal satu pekerjaan harus ditambahkan</small>
                                    </div>

                                    <!-- Technicians Management -->
                                    <div class="mb-3">
                                        <label class="form-label">Teknisi <span class="text-danger">*</span></label>

                                        <!-- Inline Technician Creation Form -->
                                        <div class="border rounded p-3 mb-3 bg-light">
                                            <div class="row">
                                                <div class="col-md-10">
                                                    <input type="text" class="form-control" id="modal_technician_name_input" placeholder="Nama teknisi...">
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-primary btn-sm w-100" id="modal_add_technician_btn">
                                                        <i class="mdi mdi-plus"></i> Tambah
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Technicians List -->
                                        <div id="modal_technicians_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                            <small class="text-muted">Belum ada teknisi ditambahkan</small>
                                        </div>
                                        <small class="text-muted">Minimal satu teknisi harus ditambahkan</small>
                                    </div>

                                    <!-- Image Uploads - 3 Column Layout -->
                                    <div class="mb-3">
                                        <label class="form-label">Upload Gambar</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="text-center border rounded p-3 mb-2" style="background-color: #f8f9fa;">
                                                    <i class="mdi mdi-camera-outline" style="font-size: 24px; color: #6c757d;"></i>
                                                    <h6 class="mt-2 mb-1">Gambar Sebelum</h6>
                                                    <input type="file" class="form-control form-control-sm" id="modal_before_images" name="before_images[]" multiple accept="image/*">
                                                    <small class="text-muted">Max 5MB</small>
                                                    <div id="modal-before-images-preview" class="mt-2"></div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="text-center border rounded p-3 mb-2" style="background-color: #f8f9fa;">
                                                    <i class="mdi mdi-camera-outline" style="font-size: 24px; color: #6c757d;"></i>
                                                    <h6 class="mt-2 mb-1">Gambar Sesudah</h6>
                                                    <input type="file" class="form-control form-control-sm" id="modal_after_images" name="after_images[]" multiple accept="image/*">
                                                    <small class="text-muted">Max 5MB</small>
                                                    <div id="modal-after-images-preview" class="mt-2"></div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="text-center border rounded p-3 mb-2" style="background-color: #f8f9fa;">
                                                    <i class="mdi mdi-camera-outline" style="font-size: 24px; color: #6c757d;"></i>
                                                    <h6 class="mt-2 mb-1">Gambar Unit</h6>
                                                    <input type="file" class="form-control form-control-sm" id="modal_unit_images" name="unit_images[]" multiple accept="image/*">
                                                    <small class="text-muted">Max 5MB</small>
                                                    <div id="modal-unit-images-preview" class="mt-2"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <small class="text-muted">Format: JPEG, PNG, JPG, GIF. Pastikan user input gambar untuk dokumentasi yang lengkap.</small>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="modal-save-btn">
                    <i class="mdi mdi-content-save"></i> Simpan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Daily Report Modal (Full Screen) -->
<div class="modal fade" id="edit-daily-report-modal" tabindex="-1" aria-labelledby="edit-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="edit-daily-report-modal-label">Edit Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner for edit modal -->
                <div id="edit-modal-loading" class="text-center py-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Menyimpan data...</p>
                </div>

                <form id="edit-modal-daily-report-form" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" id="edit-modal-daily-report-id" name="id">

                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6">
                            <!-- Unit Auto-Search -->
                            <div class="mb-3">
                                <label for="edit_modal_unit_search" class="form-label">Unit <span class="text-danger">*</span></label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="edit_modal_unit_search" placeholder="Cari unit berdasarkan kode atau tipe..." autocomplete="off">
                                    <input type="hidden" id="edit_modal_unit_id" name="unit_id" required>
                                    <div id="edit_modal_unit_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                        <!-- Search results will appear here -->
                                    </div>
                                </div>
                                <small class="text-muted">Ketik minimal 2 karakter untuk mencari unit</small>
                            </div>

                            <!-- HM -->
                            <div class="mb-3">
                                <label for="edit_modal_hm" class="form-label">HM</label>
                                <input type="number" class="form-control" id="edit_modal_hm" name="hm" step="0.01" min="0">
                            </div>

                            <!-- Problem Fields -->
                            <div class="mb-3">
                                <label for="edit_modal_problem" class="form-label">Problem</label>
                                <input type="text" class="form-control" id="edit_modal_problem" name="problem">
                            </div>

                            <div class="mb-3">
                                <label for="edit_modal_problem_component" class="form-label">Problem Component</label>
                                <input type="text" class="form-control" id="edit_modal_problem_component" name="problem_component">
                            </div>

                            <div class="mb-3">
                                <label for="edit_modal_problem_description" class="form-label">Problem Description</label>
                                <textarea class="form-control" id="edit_modal_problem_description" name="problem_description" rows="3"></textarea>
                            </div>

                            <!-- Date and Time -->
                            <div class="mb-3">
                                <label for="edit_modal_date_in" class="form-label">Tanggal <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="edit_modal_date_in" name="date_in" required>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_modal_hour_in" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                                        <input type="time" class="form-control" id="edit_modal_hour_in" name="hour_in" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_modal_hour_out" class="form-label">Jam Keluar <span class="text-danger">*</span></label>
                                        <input type="time" class="form-control" id="edit_modal_hour_out" name="hour_out" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Shift -->
                            <div class="mb-3">
                                <label for="edit_modal_shift" class="form-label">Shift <span class="text-danger">*</span></label>
                                <select class="form-control" id="edit_modal_shift" name="shift" required>
                                    <option value="">Pilih Shift</option>
                                    <option value="DAY">DAY</option>
                                    <option value="NIGHT">NIGHT</option>
                                </select>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            <!-- Jobs Management -->
                            <div class="mb-3">
                                <label class="form-label">Pekerjaan <span class="text-danger">*</span></label>

                                <!-- Inline Job Creation Form -->
                                <div class="border rounded p-3 mb-3 bg-light">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <input type="text" class="form-control" id="edit_modal_job_description_input" placeholder="Deskripsi pekerjaan...">
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="edit_modal_job_highlight_input">
                                                <label class="form-check-label" for="edit_modal_job_highlight_input">
                                                    Highlight
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-primary btn-sm w-100" id="edit_modal_add_job_btn">
                                                <i class="mdi mdi-plus"></i> Tambah
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Jobs List -->
                                <div id="edit_modal_jobs_list" style="background-color: #f8f9fa;" class="pl-3">
                                    <small class="text-muted">Belum ada pekerjaan ditambahkan</small>
                                </div>
                                <small class="text-muted">Minimal satu pekerjaan harus ditambahkan</small>
                            </div>

                            <!-- Technicians Management -->
                            <div class="mb-3">
                                <label class="form-label">Teknisi <span class="text-danger">*</span></label>

                                <!-- Inline Technician Creation Form -->
                                <div class="border rounded p-3 mb-3 bg-light">
                                    <div class="row">
                                        <div class="col-md-10">
                                            <input type="text" class="form-control" id="edit_modal_technician_name_input" placeholder="Nama teknisi...">
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-primary btn-sm w-100" id="edit_modal_add_technician_btn">
                                                <i class="mdi mdi-plus"></i> Tambah
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Technicians List -->
                                <div id="edit_modal_technicians_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                    <small class="text-muted">Belum ada teknisi ditambahkan</small>
                                </div>
                                <small class="text-muted">Minimal satu teknisi harus ditambahkan</small>
                            </div>

                            <!-- Existing Images -->
                            <div class="mb-3">
                                <label class="form-label">Gambar Existing</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>Gambar Sebelum</h6>
                                        <div id="edit-existing-before-images" class="mb-2"></div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Gambar Sesudah</h6>
                                        <div id="edit-existing-after-images" class="mb-2"></div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Gambar Unit</h6>
                                        <div id="edit-existing-unit-images" class="mb-2"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Image Uploads -->
                            <div class="mb-3">
                                <label for="edit_modal_before_images" class="form-label">Tambah Gambar Sebelum</label>
                                <input type="file" class="form-control" id="edit_modal_before_images" name="before_images[]" multiple accept="image/*">
                                <small class="text-muted">Maksimal 5MB per gambar. Format: JPEG, PNG, JPG, GIF</small>
                                <div id="edit-modal-before-images-preview" class="mt-2"></div>
                            </div>

                            <div class="mb-3">
                                <label for="edit_modal_after_images" class="form-label">Tambah Gambar Sesudah</label>
                                <input type="file" class="form-control" id="edit_modal_after_images" name="after_images[]" multiple accept="image/*">
                                <small class="text-muted">Maksimal 5MB per gambar. Format: JPEG, PNG, JPG, GIF</small>
                                <div id="edit-modal-after-images-preview" class="mt-2"></div>
                            </div>

                            <div class="mb-3">
                                <label for="edit_modal_unit_images" class="form-label">Tambah Gambar Unit</label>
                                <input type="file" class="form-control" id="edit_modal_unit_images" name="unit_images[]" multiple accept="image/*">
                                <small class="text-muted">Maksimal 5MB per gambar. Format: JPEG, PNG, JPG, GIF</small>
                                <div id="edit-modal-unit-images-preview" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="edit-modal-save-btn">
                    <i class="mdi mdi-content-save"></i> Update
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/daily-reports/index.blade.php ENDPATH**/ ?>